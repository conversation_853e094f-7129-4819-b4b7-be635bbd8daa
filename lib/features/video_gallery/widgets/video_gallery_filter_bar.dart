import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_bloc.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_event.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_state.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';

/// {@template video_gallery_filter_bar}
/// Filter bar widget for the video gallery.
///
/// Provides filter chips for different video categories.
/// {@endtemplate}
class VideoGalleryFilterBar extends StatelessWidget {
  /// {@macro video_gallery_filter_bar}
  const VideoGalleryFilterBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VideoGalleryBloc, VideoGalleryState>(
      builder: (context, state) {
        if (!state.hasVideos) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.filter_list,
                    size: 16,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.7),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Filter:',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.7),
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: VideoGalleryFilter.values.map((filter) {
                    final isSelected = state.filter == filter;
                    final count = _getFilterCount(state.videos, filter);

                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(_getFilterLabel(filter, count)),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            context.read<VideoGalleryBloc>().add(
                                  VideoGalleryFilterChanged(filter: filter),
                                );
                          }
                        },
                        avatar: _getFilterIcon(filter),
                        showCheckmark: false,
                        selectedColor:
                            Theme.of(context).primaryColor.withOpacity(0.2),
                        checkmarkColor: Theme.of(context).primaryColor,
                        labelStyle: TextStyle(
                          color: isSelected
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).colorScheme.onSurface,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Gets the label for a filter with count
  String _getFilterLabel(VideoGalleryFilter filter, int count) {
    final baseLabel = switch (filter) {
      VideoGalleryFilter.all => 'All',
      VideoGalleryFilter.highQuality => 'High Quality',
      VideoGalleryFilter.goodQuality => 'Good Quality',
      VideoGalleryFilter.recent => 'Recent',
    };

    return '$baseLabel ($count)';
  }

  /// Gets the icon for a filter
  Widget? _getFilterIcon(VideoGalleryFilter filter) {
    final iconData = switch (filter) {
      VideoGalleryFilter.all => Icons.video_library,
      VideoGalleryFilter.highQuality => Icons.star,
      VideoGalleryFilter.goodQuality => Icons.thumb_up,
      VideoGalleryFilter.recent => Icons.schedule,
    };

    return Icon(
      iconData,
      size: 16,
    );
  }

  /// Gets the count of videos for a specific filter
  int _getFilterCount(List<VideoMetadata> videos, VideoGalleryFilter filter) {
    return videos.where((video) {
      switch (filter) {
        case VideoGalleryFilter.all:
          return true;
        case VideoGalleryFilter.highQuality:
          return video.qualityScore >= 80.0;
        case VideoGalleryFilter.goodQuality:
          return video.qualityScore >= 70.0;
        case VideoGalleryFilter.recent:
          final weekAgo = DateTime.now().subtract(const Duration(days: 7));
          return video.uploadTimestamp.isAfter(weekAgo);
      }
    }).length;
  }
}
