import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_event.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_state.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_download_state.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';
import 'package:bloomg_flutter/features/video_storage/repository/firebase_video_storage_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';

/// {@template video_gallery_bloc}
/// BLoC for managing video gallery state and operations.
///
/// Handles video loading, filtering, sorting, downloading, and playback.
/// {@endtemplate}
class VideoGalleryBloc extends Bloc<VideoGalleryEvent, VideoGalleryState> {
  /// {@macro video_gallery_bloc}
  VideoGalleryBloc({
    FirebaseVideoStorageRepository? videoStorageRepository,
  })  : _videoStorageRepository =
            videoStorageRepository ?? FirebaseVideoStorageRepository(),
        super(VideoGalleryState.initial()) {
    // Register event handlers
    on<VideoGalleryLoaded>(_onVideoGalleryLoaded);
    on<VideoGalleryRefreshed>(_onVideoGalleryRefreshed);
    on<VideoSelected>(_onVideoSelected);
    on<VideoDownloadStarted>(_onVideoDownloadStarted);
    on<VideoDownloadCompleted>(_onVideoDownloadCompleted);
    on<VideoPlaybackStarted>(_onVideoPlaybackStarted);
    on<VideoPlaybackStopped>(_onVideoPlaybackStopped);
    on<VideoDeleted>(_onVideoDeleted);
    on<VideoGalleryError>(_onVideoGalleryError);
    on<VideoGalleryFilterChanged>(_onVideoGalleryFilterChanged);
    on<VideoGallerySortChanged>(_onVideoGallerySortChanged);
  }

  final FirebaseVideoStorageRepository _videoStorageRepository;
  final LoggerService _logger = LoggerService();

  StreamSubscription<List<VideoMetadata>>? _videosSubscription;

  /// Handles video gallery loaded event
  Future<void> _onVideoGalleryLoaded(
    VideoGalleryLoaded event,
    Emitter<VideoGalleryState> emit,
  ) async {
    emit(VideoGalleryState.loading());

    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Loading videos',
          'Starting video gallery load',
        ),
      );

      // Subscribe to video stream for real-time updates
      _videosSubscription?.cancel();
      _videosSubscription =
          _videoStorageRepository.getUserVideosStream().listen(
        (videos) {
          final filteredVideos = _applyFiltersAndSort(
            videos,
            state.filter,
            state.sortBy,
            state.sortAscending,
          );

          emit(
            state.copyWith(
              status: VideoGalleryStatus.loaded,
              videos: videos,
              filteredVideos: filteredVideos,
            ),
          );

          _logger.info(
            LoggingConstants.formatMessage(
              LoggingConstants.videoGalleryModule,
              'Videos loaded',
              'Count: ${videos.length}, Filtered: ${filteredVideos.length}',
            ),
          );
        },
        onError: (Object error, StackTrace stackTrace) {
          add(VideoGalleryError(error: error, stackTrace: stackTrace));
        },
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.criticalError,
          'Failed to load videos: $error',
          'Event: VideoGalleryLoaded',
        ),
        error,
        stackTrace,
      );

      add(VideoGalleryError(error: error, stackTrace: stackTrace));
    }
  }

  /// Handles video gallery refreshed event
  Future<void> _onVideoGalleryRefreshed(
    VideoGalleryRefreshed event,
    Emitter<VideoGalleryState> emit,
  ) async {
    emit(
      VideoGalleryState.refreshing(
        videos: state.videos,
        filteredVideos: state.filteredVideos,
        filter: state.filter,
        sortBy: state.sortBy,
        sortAscending: state.sortAscending,
      ),
    );

    try {
      final videos = await _videoStorageRepository.getUserVideos();
      final filteredVideos = _applyFiltersAndSort(
        videos,
        state.filter,
        state.sortBy,
        state.sortAscending,
      );

      emit(
        state.copyWith(
          status: VideoGalleryStatus.loaded,
          videos: videos,
          filteredVideos: filteredVideos,
        ),
      );

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Videos refreshed',
          'Count: ${videos.length}',
        ),
      );
    } catch (error, stackTrace) {
      add(VideoGalleryError(error: error, stackTrace: stackTrace));
    }
  }

  /// Handles video selected event
  Future<void> _onVideoSelected(
    VideoSelected event,
    Emitter<VideoGalleryState> emit,
  ) async {
    emit(state.copyWith(selectedVideo: event.video));

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Video selected',
        'VideoId: ${event.video.id}, Quality: ${event.video.qualityScore}%',
      ),
    );
  }

  /// Handles video download started event
  Future<void> _onVideoDownloadStarted(
    VideoDownloadStarted event,
    Emitter<VideoGalleryState> emit,
  ) async {
    final downloadStates =
        Map<String, VideoDownloadState>.from(state.downloadStates);
    downloadStates[event.video.id] =
        VideoDownloadState.downloading(progressPercentage: 0);

    emit(state.copyWith(downloadStates: downloadStates));

    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Download started',
          'VideoId: ${event.video.id}',
        ),
      );

      final localPath =
          await _videoStorageRepository.downloadVideoToLocal(event.video);

      add(
        VideoDownloadCompleted(
          video: event.video,
          localPath: localPath,
        ),
      );
    } catch (error, stackTrace) {
      final updatedDownloadStates =
          Map<String, VideoDownloadState>.from(downloadStates);
      updatedDownloadStates[event.video.id] = VideoDownloadState.failed(
        error: error.toString(),
      );

      emit(state.copyWith(downloadStates: updatedDownloadStates));

      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Download failed: $error',
          'VideoId: ${event.video.id}',
        ),
        error,
        stackTrace,
      );
    }
  }

  /// Handles video download completed event
  Future<void> _onVideoDownloadCompleted(
    VideoDownloadCompleted event,
    Emitter<VideoGalleryState> emit,
  ) async {
    final downloadStates =
        Map<String, VideoDownloadState>.from(state.downloadStates);
    downloadStates[event.video.id] = VideoDownloadState.downloaded(
      localFilePath: event.localPath,
    );

    emit(state.copyWith(downloadStates: downloadStates));

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Download completed',
        'VideoId: ${event.video.id}, Path: ${event.localPath}',
      ),
    );
  }

  /// Handles video playback started event
  Future<void> _onVideoPlaybackStarted(
    VideoPlaybackStarted event,
    Emitter<VideoGalleryState> emit,
  ) async {
    emit(
      state.copyWith(
        playingVideo: event.video,
        playbackUrl: event.videoUrl,
      ),
    );

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Playback started',
        'VideoId: ${event.video.id}',
      ),
    );
  }

  /// Handles video playback stopped event
  Future<void> _onVideoPlaybackStopped(
    VideoPlaybackStopped event,
    Emitter<VideoGalleryState> emit,
  ) async {
    emit(
      state.copyWith(),
    );

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Playback stopped',
        'User action',
      ),
    );
  }

  /// Handles video deleted event
  Future<void> _onVideoDeleted(
    VideoDeleted event,
    Emitter<VideoGalleryState> emit,
  ) async {
    try {
      await _videoStorageRepository.deleteVideo(event.video);

      // Remove from download states
      final downloadStates =
          Map<String, VideoDownloadState>.from(state.downloadStates);
      downloadStates.remove(event.video.id);

      // Remove from videos list
      final videos = List<VideoMetadata>.from(state.videos);
      videos.removeWhere((video) => video.id == event.video.id);

      final filteredVideos = _applyFiltersAndSort(
        videos,
        state.filter,
        state.sortBy,
        state.sortAscending,
      );

      emit(
        state.copyWith(
          videos: videos,
          filteredVideos: filteredVideos,
          downloadStates: downloadStates,
          selectedVideo: state.selectedVideo?.id == event.video.id
              ? null
              : state.selectedVideo,
          playingVideo: state.playingVideo?.id == event.video.id
              ? null
              : state.playingVideo,
          playbackUrl: state.playingVideo?.id == event.video.id
              ? null
              : state.playbackUrl,
        ),
      );

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Video deleted',
          'VideoId: ${event.video.id}',
        ),
      );
    } catch (error, stackTrace) {
      add(VideoGalleryError(error: error, stackTrace: stackTrace));
    }
  }

  /// Handles video gallery error event
  Future<void> _onVideoGalleryError(
    VideoGalleryError event,
    Emitter<VideoGalleryState> emit,
  ) async {
    _logger.error(
      LoggingConstants.formatError(
        LoggingConstants.videoGalleryModule,
        LoggingConstants.criticalError,
        'Gallery error: ${event.error}',
        'State: ${state.status}',
      ),
      event.error,
      event.stackTrace,
    );

    emit(
      VideoGalleryState.error(
        error: event.error,
        stackTrace: event.stackTrace,
        videos: state.videos,
        filteredVideos: state.filteredVideos,
      ),
    );
  }

  /// Handles video gallery filter changed event
  Future<void> _onVideoGalleryFilterChanged(
    VideoGalleryFilterChanged event,
    Emitter<VideoGalleryState> emit,
  ) async {
    final filteredVideos = _applyFiltersAndSort(
      state.videos,
      event.filter,
      state.sortBy,
      state.sortAscending,
    );

    emit(
      state.copyWith(
        filter: event.filter,
        filteredVideos: filteredVideos,
      ),
    );

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Filter changed',
        'Filter: ${event.filter}, Results: ${filteredVideos.length}',
      ),
    );
  }

  /// Handles video gallery sort changed event
  Future<void> _onVideoGallerySortChanged(
    VideoGallerySortChanged event,
    Emitter<VideoGalleryState> emit,
  ) async {
    final filteredVideos = _applyFiltersAndSort(
      state.videos,
      state.filter,
      event.sortBy,
      event.ascending,
    );

    emit(
      state.copyWith(
        sortBy: event.sortBy,
        sortAscending: event.ascending,
        filteredVideos: filteredVideos,
      ),
    );

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Sort changed',
        'SortBy: ${event.sortBy}, Ascending: ${event.ascending}',
      ),
    );
  }

  /// Applies filters and sorting to the video list
  List<VideoMetadata> _applyFiltersAndSort(
    List<VideoMetadata> videos,
    VideoGalleryFilter filter,
    VideoSortField sortBy,
    bool ascending,
  ) {
    // Apply filter
    final filtered = videos.where((video) {
      switch (filter) {
        case VideoGalleryFilter.all:
          return true;
        case VideoGalleryFilter.highQuality:
          return video.qualityScore >= 80.0;
        case VideoGalleryFilter.goodQuality:
          return video.qualityScore >= 70.0;
        case VideoGalleryFilter.recent:
          final weekAgo = DateTime.now().subtract(const Duration(days: 7));
          return video.uploadTimestamp.isAfter(weekAgo);
      }
    }).toList();

    // Apply sorting
    filtered.sort((a, b) {
      int comparison;
      switch (sortBy) {
        case VideoSortField.uploadDate:
          comparison = a.uploadTimestamp.compareTo(b.uploadTimestamp);
        case VideoSortField.qualityScore:
          comparison = a.qualityScore.compareTo(b.qualityScore);
        case VideoSortField.fileSize:
          comparison = (a.fileSize ?? 0).compareTo(b.fileSize ?? 0);
        case VideoSortField.fileName:
          comparison = a.fileName.compareTo(b.fileName);
      }

      return ascending ? comparison : -comparison;
    });

    return filtered;
  }

  @override
  Future<void> close() {
    _videosSubscription?.cancel();
    _videoStorageRepository.dispose();
    return super.close();
  }
}
