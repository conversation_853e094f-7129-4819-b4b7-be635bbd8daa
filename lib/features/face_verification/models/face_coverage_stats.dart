import 'dart:math';

import 'package:equatable/equatable.dart';

/// {@template face_coverage_stats}
/// Model representing face coverage statistics during video recording.
///
/// Tracks face detection quality metrics and coverage percentages
/// to determine if a video meets verification requirements.
/// {@endtemplate}
class FaceCoverageStats extends Equatable {
  /// {@macro face_coverage_stats}
  const FaceCoverageStats({
    required this.totalFrames,
    required this.framesWithFace,
    required this.averageCoverage,
    required this.qualityScore,
    this.peakCoverage = 0.0,
    this.minimumCoverage = 0.0,
  });

  /// Creates initial empty stats
  factory FaceCoverageStats.empty() {
    return const FaceCoverageStats(
      totalFrames: 0,
      framesWithFace: 0,
      averageCoverage: 0,
      qualityScore: 0,
    );
  }

  /// Creates stats from face detection results
  factory FaceCoverageStats.fromDetectionResults(
    List<double> coveragePercentages,
  ) {
    if (coveragePercentages.isEmpty) {
      return FaceCoverageStats.empty();
    }

    final totalFrames = coveragePercentages.length;
    final framesWithFace = coveragePercentages.where((c) => c > 0).length;
    final averageCoverage = coveragePercentages.isNotEmpty
        ? coveragePercentages.reduce((a, b) => a + b) /
            coveragePercentages.length
        : 0.0;

    final peakCoverage = coveragePercentages.isNotEmpty
        ? coveragePercentages.reduce((a, b) => a > b ? a : b)
        : 0.0;

    final minimumCoverage = coveragePercentages.isNotEmpty
        ? coveragePercentages.reduce((a, b) => a < b ? a : b)
        : 0.0;

    // Calculate quality score based on multiple factors
    final faceDetectionRate = framesWithFace / totalFrames;
    final coverageQuality = averageCoverage / 100.0;
    final consistencyBonus = _calculateConsistencyBonus(coveragePercentages);

    final qualityScore = (faceDetectionRate * 40 +
            coverageQuality * 40 +
            consistencyBonus * 20) *
        100;

    return FaceCoverageStats(
      totalFrames: totalFrames,
      framesWithFace: framesWithFace,
      averageCoverage: averageCoverage,
      qualityScore: qualityScore.clamp(0.0, 100.0),
      peakCoverage: peakCoverage,
      minimumCoverage: minimumCoverage,
    );
  }

  /// Total number of frames processed
  final int totalFrames;

  /// Number of frames where a face was detected
  final int framesWithFace;

  /// Average face coverage percentage across all frames
  final double averageCoverage;

  /// Overall quality score (0-100) based on coverage and consistency
  final double qualityScore;

  /// Peak face coverage percentage achieved
  final double peakCoverage;

  /// Minimum face coverage percentage detected
  final double minimumCoverage;

  /// Calculates consistency bonus based on coverage variance
  static double _calculateConsistencyBonus(List<double> coverages) {
    if (coverages.length < 2) return 0;

    final mean = coverages.reduce((a, b) => a + b) / coverages.length;
    final variance =
        coverages.map((c) => (c - mean) * (c - mean)).reduce((a, b) => a + b) /
            coverages.length;

    // Lower variance = higher consistency = higher bonus
    final standardDeviation = sqrt(variance);
    final consistencyScore = (100 - standardDeviation).clamp(0.0, 100.0);

    return consistencyScore / 100.0;
  }

  /// Creates a copy of this stats with updated fields
  FaceCoverageStats copyWith({
    int? totalFrames,
    int? framesWithFace,
    double? averageCoverage,
    double? qualityScore,
    double? peakCoverage,
    double? minimumCoverage,
  }) {
    return FaceCoverageStats(
      totalFrames: totalFrames ?? this.totalFrames,
      framesWithFace: framesWithFace ?? this.framesWithFace,
      averageCoverage: averageCoverage ?? this.averageCoverage,
      qualityScore: qualityScore ?? this.qualityScore,
      peakCoverage: peakCoverage ?? this.peakCoverage,
      minimumCoverage: minimumCoverage ?? this.minimumCoverage,
    );
  }

  /// Whether this recording meets the quality threshold for verification
  bool get meetsQualityThreshold => qualityScore >= 70.0;

  /// Face detection rate as a percentage
  double get faceDetectionRate =>
      totalFrames > 0 ? (framesWithFace / totalFrames) * 100 : 0.0;

  /// Quality rating based on score
  String get qualityRating {
    if (qualityScore >= 80) return 'Excellent';
    if (qualityScore >= 70) return 'Good';
    if (qualityScore >= 50) return 'Moderate';
    return 'Poor';
  }

  /// Color associated with quality rating
  String get qualityColor {
    if (qualityScore >= 80) return 'green';
    if (qualityScore >= 70) return 'teal';
    if (qualityScore >= 50) return 'amber';
    return 'red';
  }

  @override
  List<Object?> get props => [
        totalFrames,
        framesWithFace,
        averageCoverage,
        qualityScore,
        peakCoverage,
        minimumCoverage,
      ];
}
