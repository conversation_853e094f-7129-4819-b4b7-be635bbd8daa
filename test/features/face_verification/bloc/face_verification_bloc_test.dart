import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_verification_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_verification_event.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_verification_state.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_upload_progress.dart';
import 'package:bloomg_flutter/features/video_storage/repository/firebase_video_storage_repository.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFirebaseVideoStorageRepository extends Mock
    implements FirebaseVideoStorageRepository {}

class MockLoggerService extends Mock implements LoggerService {}

void main() {
  group('FaceVerificationBloc', () {
    late FaceVerificationBloc faceVerificationBloc;
    late MockFirebaseVideoStorageRepository mockRepository;
    late MockLoggerService mockLogger;

    setUp(() {
      mockRepository = MockFirebaseVideoStorageRepository();
      mockLogger = MockLoggerService();
      faceVerificationBloc = FaceVerificationBloc(
        videoStorageRepository: mockRepository,
      );
    });

    tearDown(() {
      faceVerificationBloc.close();
    });

    test('initial state is correct', () {
      expect(
        faceVerificationBloc.state,
        equals(const FaceVerificationState()),
      );
    });

    group('FaceVerificationStarted', () {
      const config = VideoCaptureConfig(
        duration: 9,
        qualityThreshold: 80,
        frameRate: 30,
        resolution: VideoCaptureResolution.hd720,
      );

      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'starts face verification with countdown',
        build: () => faceVerificationBloc,
        act: (bloc) => bloc.add(const FaceVerificationStarted(config: config)),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.countdown,
            config: config,
            countdownSeconds: 3,
          ),
        ],
        verify: (_) {
          verify(
            () => mockLogger.info(
              '[FACE_VERIFICATION] Action: Starting verification with 9s duration',
            ),
          ).called(1);
        },
      );
    });

    group('CountdownTick', () {
      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'decrements countdown and starts recording when reaching 0',
        build: () => faceVerificationBloc,
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.countdown,
          countdownSeconds: 1,
        ),
        act: (bloc) => bloc.add(const RecordingCountdownStarted()),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.recording,
            recordingStartTime: null, // Will be set to actual time
          ),
        ],
        verify: (_) {
          verify(
            () => mockLogger
                .info('[FACE_VERIFICATION] Action: Recording started'),
          ).called(1);
        },
      );

      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'decrements countdown when not at 0',
        build: () => faceVerificationBloc,
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.countdown,
          countdownSeconds: 3,
        ),
        act: (bloc) => bloc.add(const RecordingCountdownStarted()),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.countdown,
            countdownSeconds: 2,
          ),
        ],
      );
    });

    group('FaceDetected', () {
      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'updates face detection metrics during recording',
        build: () => faceVerificationBloc,
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.recording,
          recordingStartTime: null,
        ),
        act: (bloc) => bloc.add(
          const FaceDetectionFrameProcessed(
            coveragePercentage: 85.5,
            faceDetected: true,
            qualityScore: 92,
          ),
        ),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.recording,
            recordingStartTime: null,
            currentFaceCount: 1,
            currentCoveragePercentage: 85.5,
            currentQualityScore: 92.0,
            averageQualityScore: 92.0,
            totalFramesProcessed: 1,
            framesWithFace: 1,
            meetsQualityThreshold: true,
          ),
        ],
      );

      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'calculates average quality score over multiple frames',
        build: () => faceVerificationBloc,
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.recording,
          recordingStartTime: null,
          averageQualityScore: 80.0,
          totalFramesProcessed: 2,
          framesWithFace: 2,
        ),
        act: (bloc) => bloc.add(
          const FaceDetectionFrameProcessed(
            coveragePercentage: 90,
            faceDetected: true,
            qualityScore: 95,
          ),
        ),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.recording,
            recordingStartTime: null,
            currentFaceCount: 1,
            currentCoveragePercentage: 90,
            currentQualityScore: 95.0,
            averageQualityScore: 85.0, // (80*2 + 95) / 3 = 85
            totalFramesProcessed: 3,
            framesWithFace: 3,
            meetsQualityThreshold: true,
          ),
        ],
      );

      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'handles frames without face detection',
        build: () => faceVerificationBloc,
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.recording,
          recordingStartTime: null,
          totalFramesProcessed: 1,
          framesWithFace: 1,
        ),
        act: (bloc) => bloc.add(
          const FaceDetectionFrameProcessed(
            coveragePercentage: 0,
            faceDetected: false,
            qualityScore: 0,
          ),
        ),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.recording,
            recordingStartTime: null,
            currentFaceCount: 0,
            currentQualityScore: 0.0,
            averageQualityScore: 0.0, // No face detected
            totalFramesProcessed: 2,
            framesWithFace: 1, // Unchanged
          ),
        ],
      );
    });

    group('RecordingCompleted', () {
      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'transitions to validation after recording completion',
        build: () => faceVerificationBloc,
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.recording,
          averageQualityScore: 85.0,
          totalFramesProcessed: 100,
          framesWithFace: 90,
        ),
        act: (bloc) =>
            bloc.add(const RecordingCompleted(videoPath: '/path/to/video.mp4')),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.validating,
            videoPath: '/path/to/video.mp4',
            averageQualityScore: 85.0,
            totalFramesProcessed: 100,
            framesWithFace: 90,
            faceCoveragePercentage: 90.0, // 90/100 * 100
          ),
        ],
        verify: (_) {
          verify(
            () => mockLogger.info(
              '[FACE_VERIFICATION] Action: Recording completed, starting validation',
            ),
          ).called(1);
        },
      );
    });

    group('ValidationCompleted', () {
      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'starts upload after successful validation',
        build: () {
          when(() => mockRepository.uploadVideoStream(any(), any())).thenAnswer(
            (_) => Stream.value(
              const VideoUploadProgress(
                videoId: 'test-video-id',
                status: VideoUploadStatus.completed,
                progressPercentage: 100,
              ),
            ),
          );
          return faceVerificationBloc;
        },
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.validating,
          videoPath: '/path/to/video.mp4',
          averageQualityScore: 85.0,
        ),
        act: (bloc) => bloc.add(
          const VideoValidationCompleted(
            qualityScore: 85,
            meetsThreshold: true,
          ),
        ),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.uploading,
            videoPath: '/path/to/video.mp4',
            qualityScore: 85,
            meetsQualityThreshold: true,
          ),
          const FaceVerificationState(
            status: FaceVerificationStatus.uploading,
            videoPath: '/path/to/video.mp4',
            averageQualityScore: 85.0,
            isValidVideo: true,
            uploadProgress: VideoUploadProgress(
              videoId: 'test-video-id',
              status: VideoUploadStatus.completed,
              progressPercentage: 100,
            ),
          ),
          const FaceVerificationState(
            status: FaceVerificationStatus.completed,
            videoPath: '/path/to/video.mp4',
            averageQualityScore: 85.0,
            isValidVideo: true,
            uploadProgress: VideoUploadProgress(
              videoId: 'test-video-id',
              status: VideoUploadStatus.completed,
              progressPercentage: 100,
            ),
          ),
        ],
        verify: (_) {
          verify(
            () => mockLogger.info(
              '[FACE_VERIFICATION] Action: Validation completed - valid: true',
            ),
          ).called(1);
          verify(
            () => mockLogger.info(
              '[FACE_VERIFICATION] Action: Starting video upload',
            ),
          ).called(1);
          verify(
            () => mockLogger.info(
              '[FACE_VERIFICATION] Action: Upload completed successfully',
            ),
          ).called(1);
        },
      );

      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'transitions to failed state when validation fails',
        build: () => faceVerificationBloc,
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.validating,
          videoPath: '/path/to/video.mp4',
          averageQualityScore: 45.0,
        ),
        act: (bloc) => bloc.add(
          const VideoValidationCompleted(
            qualityScore: 45,
            meetsThreshold: false,
          ),
        ),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.error,
            videoPath: '/path/to/video.mp4',
            qualityScore: 45,
            error: 'Video quality does not meet minimum requirements',
          ),
        ],
        verify: (_) {
          verify(
            () => mockLogger.info(
              '[FACE_VERIFICATION] Action: Validation completed - valid: false',
            ),
          ).called(1);
          verify(
            () => mockLogger.warning(
              '[FACE_VERIFICATION] Action: Video validation failed - quality too low',
            ),
          ).called(1);
        },
      );
    });

    group('UploadProgressUpdated', () {
      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'updates upload progress during upload',
        build: () => faceVerificationBloc,
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.uploading,
        ),
        act: (bloc) => bloc.add(
          const UploadProgressUpdated(
            progress: VideoUploadProgress(
              videoId: 'test-video',
              status: VideoUploadStatus.uploading,
              progressPercentage: 65,
              bytesTransferred: 650000,
              totalBytes: 1000000,
            ),
          ),
        ),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.uploading,
            uploadProgress: VideoUploadProgress(
              videoId: 'test-video',
              status: VideoUploadStatus.uploading,
              progressPercentage: 65,
              bytesTransferred: 650000,
              totalBytes: 1000000,
            ),
          ),
        ],
      );
    });

    group('FaceVerificationReset', () {
      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'resets to initial state',
        build: () => faceVerificationBloc,
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.completed,
          videoPath: '/path/to/video.mp4',
          averageQualityScore: 85.0,
          isValidVideo: true,
        ),
        act: (bloc) => bloc.add(const FaceVerificationReset()),
        expect: () => [
          const FaceVerificationState(),
        ],
        verify: (_) {
          verify(
            () => mockLogger.info(
              '[FACE_VERIFICATION] Action: Resetting verification state',
            ),
          ).called(1);
        },
      );
    });

    group('error handling', () {
      blocTest<FaceVerificationBloc, FaceVerificationState>(
        'handles upload failure',
        build: () {
          when(() => mockRepository.uploadVideoStream(any(), any())).thenAnswer(
            (_) => Stream.value(
              const VideoUploadProgress(
                videoId: 'test-video-id',
                status: VideoUploadStatus.failed,
                error: 'Network error',
              ),
            ),
          );
          return faceVerificationBloc;
        },
        seed: () => const FaceVerificationState(
          status: FaceVerificationStatus.validating,
          videoPath: '/path/to/video.mp4',
          averageQualityScore: 85.0,
        ),
        act: (bloc) => bloc.add(const ValidationCompleted(isValid: true)),
        expect: () => [
          const FaceVerificationState(
            status: FaceVerificationStatus.uploading,
            videoPath: '/path/to/video.mp4',
            averageQualityScore: 85.0,
            isValidVideo: true,
          ),
          const FaceVerificationState(
            status: FaceVerificationStatus.uploading,
            videoPath: '/path/to/video.mp4',
            averageQualityScore: 85.0,
            isValidVideo: true,
            uploadProgress: VideoUploadProgress(
              videoId: 'test-video-id',
              status: VideoUploadStatus.failed,
              error: 'Network error',
            ),
          ),
          const FaceVerificationState(
            status: FaceVerificationStatus.failed,
            videoPath: '/path/to/video.mp4',
            averageQualityScore: 85.0,
            isValidVideo: true,
            uploadProgress: VideoUploadProgress(
              videoId: 'test-video-id',
              status: VideoUploadStatus.failed,
              error: 'Network error',
            ),
            error: 'Upload failed: Network error',
          ),
        ],
        verify: (_) {
          verify(
            () => mockLogger.error(
              '[FACE_VERIFICATION] Action: Upload failed - Network error',
            ),
          ).called(1);
        },
      );
    });
  });
}
